const crypto = require("crypto");
const ErrorResponse = require("../utils/errorResponse");
const User = require("../models/User");
const TempUser = require("../models/TempUser");
const sendEmail = require("../utils/sendEmail");
const { sendOTP } = require("../utils/sendOTP");
const { validationResult } = require("express-validator");
const { verifyIdToken } = require("../config/firebase");

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { firstName, lastName, email, mobile, role } = req.body;

    // Check if email already exists in User collection
    const existingUserEmail = await User.findOne({ email });
    if (existingUserEmail) {
      return next(new ErrorResponse("Email already registered", 400));
    }

    // Check if mobile already exists in User collection
    const existingUserMobile = await User.findOne({ mobile });
    if (existingUserMobile) {
      return next(new ErrorResponse("Mobile number already registered", 400));
    }

    // Check if email already exists in TempUser collection and remove if exists
    await TempUser.findOneAndDelete({ email });

    // Check if mobile already exists in TempUser collection and remove if exists
    await TempUser.findOneAndDelete({ mobile });

    // Create temporary user for OTP verification (always create temp user to maintain flow)
    const tempUser = await TempUser.create({
      firstName,
      lastName,
      email,
      mobile,
      role: role || "buyer",
    });

    // Generate OTP for verification
    const otp = tempUser.generateOTP();
    await tempUser.save({ validateBeforeSave: false });

    // Check if OTP is disabled via environment variable
    const otpEnabled = process.env.ENABLE_OTP === 'true';

    try {
      if (otpEnabled) {
        // Send OTP via SMS and Email when OTP is enabled
        const otpResult = await sendOTP(tempUser, otp);

        if (!otpResult.success) {
          return next(new ErrorResponse("Failed to send verification code", 500));
        }
      }

      res.status(201).json({
        success: true,
        message: otpEnabled
          ? "Registration successful. Please verify with the OTP sent to your email and mobile."
          : "Registration successful. Please verify with the OTP displayed below.",
        userId: tempUser._id,
        cooldownSeconds: parseInt(process.env.OTP_RESEND_COOLDOWN) || 60,
        // Return OTP in development mode OR when OTP is disabled
        ...((process.env.NODE_ENV === 'development' || !otpEnabled) && { developmentOtp: otp })
      });
    } catch (err) {
      console.log(err);
      tempUser.otpCode = undefined;
      tempUser.otpExpire = undefined;

      await tempUser.save({ validateBeforeSave: false });

      return next(new ErrorResponse("Failed to send verification code", 500));
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Send OTP for login or resend for signup
// @route   POST /api/auth/send-otp
// @access  Public
exports.sendOTP = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { email, mobile, userId } = req.body;

    let user = null;
    let tempUser = null;

    // If userId is provided, check if it's a temporary user (for resend during signup)
    if (userId) {
      tempUser = await TempUser.findById(userId);
      if (!tempUser) {
        user = await User.findById(userId);
        if (!user) {
          return next(new ErrorResponse("User not found", 404));
        }
      }
    } else {
      // Check for user by email or mobile (for login)
      user = await User.findOne({
        $or: [
          { email: email },
          { mobile: mobile }
        ]
      });

      if (!user) {
        return next(new ErrorResponse("User not found", 404));
      }
    }

    const targetUser = tempUser || user;

    // Check if OTP is disabled via environment variable
    const otpEnabled = process.env.ENABLE_OTP === 'true';
    if (!otpEnabled && user) {
      // If OTP is disabled and this is a login request, directly log in the user
      user.isVerified = true;
      user.lastLogin = Date.now();
      await user.save({ validateBeforeSave: false });

      return sendTokenResponse(user, 200, res);
    }

    // Generate OTP
    const otp = targetUser.generateOTP();
    await targetUser.save({ validateBeforeSave: false });

    try {
      // Send OTP via SMS and Email
      const otpResult = await sendOTP(targetUser, otp);

      if (!otpResult.success) {
        return next(new ErrorResponse("Failed to send verification code", 500));
      }

      res.status(200).json({
        success: true,
        message: "Verification code sent successfully",
        userId: targetUser._id,
        // Only return OTP in development mode (when NODE_ENV is development)
        ...(process.env.NODE_ENV === 'development' && { developmentOtp: otp })
      });
    } catch (err) {
      console.log(err);
      targetUser.otpCode = undefined;
      targetUser.otpExpire = undefined;

      await targetUser.save({ validateBeforeSave: false });

      return next(new ErrorResponse("Failed to send verification code", 500));
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Verify OTP and login
// @route   POST /api/auth/verify-otp
// @access  Public
exports.verifyOTP = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { userId, otp } = req.body;

    // First, try to find in TempUser collection (for signup verification)
    let tempUser = await TempUser.findById(userId);
    let user = null;

    if (tempUser) {
      // This is a signup verification

      // Check if OTP exists and is valid
      if (!tempUser.otpCode || !tempUser.otpExpire) {
        return next(new ErrorResponse("No OTP was generated or it has expired", 400));
      }

      // Check if OTP has expired
      if (tempUser.otpExpire < Date.now()) {
        return next(new ErrorResponse("OTP has expired", 400));
      }

      // Check if OTP matches
      if (tempUser.otpCode !== otp) {
        return next(new ErrorResponse("Invalid OTP", 400));
      }

      // OTP is valid, create the actual user in User collection
      user = await User.create({
        firstName: tempUser.firstName,
        lastName: tempUser.lastName,
        email: tempUser.email,
        mobile: tempUser.mobile,
        role: tempUser.role,
        isVerified: true,
        lastLogin: Date.now()
      });

      // Remove the temporary user
      await TempUser.findByIdAndDelete(tempUser._id);

    } else {
      // Try to find in User collection (for login verification)
      user = await User.findById(userId);

      if (!user) {
        return next(new ErrorResponse("User not found", 404));
      }

      // Check if OTP exists and is valid
      if (!user.otpCode || !user.otpExpire) {
        return next(new ErrorResponse("No OTP was generated or it has expired", 400));
      }

      // Check if OTP has expired
      if (user.otpExpire < Date.now()) {
        return next(new ErrorResponse("OTP has expired", 400));
      }

      // Check if OTP matches
      if (user.otpCode !== otp) {
        return next(new ErrorResponse("Invalid OTP", 400));
      }

      // Clear OTP fields
      user.otpCode = undefined;
      user.otpExpire = undefined;

      // Set user as verified if not already
      if (!user.isVerified) {
        user.isVerified = true;
      }

      // Update last login
      user.lastLogin = Date.now();
      await user.save({ validateBeforeSave: false });
    }

    // Send token response
    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Login with mobile number (send OTP)
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { mobile } = req.body;

    // Check for user by mobile
    const user = await User.findOne({ mobile });

    if (!user) {
      return next(new ErrorResponse("User not found with this mobile number", 404));
    }

    // Generate OTP (always generate to maintain flow)
    const otp = user.generateOTP();
    await user.save({ validateBeforeSave: false });

    // Check if OTP is disabled via environment variable
    const otpEnabled = process.env.ENABLE_OTP === 'true';

    try {
      if (otpEnabled) {
        // Send OTP via SMS and Email when OTP is enabled
        const otpResult = await sendOTP(user, otp);

        if (!otpResult.success) {
          return next(new ErrorResponse("Failed to send verification code", 500));
        }
      }

      res.status(200).json({
        success: true,
        message: otpEnabled
          ? "Verification code sent successfully"
          : "Please verify with the OTP displayed below",
        userId: user._id,
        // Return OTP in development mode OR when OTP is disabled
        ...((process.env.NODE_ENV === 'development' || !otpEnabled) && { developmentOtp: otp })
      });
    } catch (err) {
      console.log(err);
      user.otpCode = undefined;
      user.otpExpire = undefined;

      await user.save({ validateBeforeSave: false });

      return next(new ErrorResponse("Failed to send verification code", 500));
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Verify email
// @route   GET /api/auth/verify-email/:token
// @access  Public
exports.verifyEmail = async (req, res, next) => {
  try {
    // Get hashed token
    const emailVerificationToken = crypto
      .createHash("sha256")
      .update(req.params.token)
      .digest("hex");

    const user = await User.findOne({
      emailVerificationToken,
      emailVerificationExpire: { $gt: Date.now() },
    });

    if (!user) {
      return next(new ErrorResponse("Invalid token", 400));
    }

    // Set isVerified to true and remove verification token
    user.isVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpire = undefined;

    await user.save();

    res.status(200).json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update current logged in user
// @route   PUT /api/auth/update
// @access  Private
exports.updateMe = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Fields that can be updated (excluding email and mobile)
    const fieldsToUpdate = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      profileImage: req.body.profileImage,
    };

    // If user has seller access, allow updating seller info (use effective role)
    const effectiveRole = req.user.role === 'admin' ? req.user.role : req.user.activeRole;
    if ((effectiveRole === "seller" || req.user.role === "admin") && req.body.sellerInfo) {
      fieldsToUpdate.sellerInfo = req.body.sellerInfo;
    }

    // Remove undefined fields
    Object.keys(fieldsToUpdate).forEach(key => {
      if (fieldsToUpdate[key] === undefined) {
        delete fieldsToUpdate[key];
      }
    });

    const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
      new: true,
      runValidators: true,
    });

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
exports.forgotPassword = async (req, res, next) => {
  try {
    const user = await User.findOne({ email: req.body.email });

    if (!user) {
      return next(new ErrorResponse("There is no user with that email", 404));
    }

    // Get reset token
    const resetToken = crypto.randomBytes(20).toString("hex");

    // Hash token and set to resetPasswordToken field
    user.resetPasswordToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Set expire
    user.resetPasswordExpire = Date.now() + 10 * 60 * 1000;

    await user.save({ validateBeforeSave: false });

    // Create reset url
    const resetUrl = `${req.protocol}://${req.get(
      "host"
    )}/api/auth/reset-password/${resetToken}`;

    const message = `You are receiving this email because you (or someone else) has requested the reset of a password. Please click the link to reset your password: \n\n ${resetUrl}`;

    try {
      await sendEmail({
        email: user.email,
        subject: "Password Reset Token",
        message,
      });

      res.status(200).json({ success: true, data: "Email sent" });
    } catch (err) {
      console.log(err);
      user.resetPasswordToken = undefined;
      user.resetPasswordExpire = undefined;

      await user.save({ validateBeforeSave: false });

      return next(new ErrorResponse("Email could not be sent", 500));
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Reset password
// @route   PUT /api/auth/reset-password/:token
// @access  Public
exports.resetPassword = async (req, res, next) => {
  try {
    // Get hashed token
    const resetPasswordToken = crypto
      .createHash("sha256")
      .update(req.params.token)
      .digest("hex");

    const user = await User.findOne({
      resetPasswordToken,
      resetPasswordExpire: { $gt: Date.now() },
    });

    if (!user) {
      return next(new ErrorResponse("Invalid token", 400));
    }

    // Set new password
    user.password = req.body.password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Update password
// @route   PUT /api/auth/update-password
// @access  Private
exports.updatePassword = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select("+password");

    // Check current password
    if (!(await user.matchPassword(req.body.currentPassword))) {
      return next(new ErrorResponse("Password is incorrect", 401));
    }

    user.password = req.body.newPassword;
    await user.save();

    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Log user out / clear cookie
// @route   GET /api/auth/logout
// @access  Private
exports.logout = async (req, res, next) => {
  res.status(200).json({
    success: true,
    data: {},
  });
};

// Helper function to get token from model, create cookie and send response
const sendTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();

  // Use JWT_COOKIE_EXPIRE from env or default to 30 days if not set
  const cookieExpire = process.env.JWT_COOKIE_EXPIRE || 30;

  const options = {
    expires: new Date(Date.now() + cookieExpire * 24 * 60 * 60 * 1000),
    httpOnly: true,
  };

  if (process.env.NODE_ENV === "production") {
    options.secure = true;
  }

  // Prepare user data to send (exclude sensitive fields)
  const userData = {
    _id: user._id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    mobile: user.mobile,
    profileImage: user.profileImage,
    role: user.role,
    // Include activeRole for non-admin users
    ...(user.role !== 'admin' && { activeRole: user.activeRole }),
    isVerified: user.isVerified,
    createdAt: user.createdAt,
    lastLogin: user.lastLogin,
    // Include sellerInfo when user is in seller mode (either role=seller or activeRole=seller)
    ...(user.role === 'seller' || user.activeRole === 'seller') && { sellerInfo: user.sellerInfo },
    // Include paymentInfo for all users (needed for Stripe configuration)
    ...(user.paymentInfo && { paymentInfo: user.paymentInfo }),
  };

  res.status(statusCode).cookie("token", token, options).json({
    success: true,
    token,
    user: userData,
  });
};

// @desc    Google Sign-In (for existing users)
// @route   POST /api/auth/google
// @access  Public
exports.googleAuth = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { idToken } = req.body;

    // Verify the Firebase ID token
    let decodedToken;
    try {
      decodedToken = await verifyIdToken(idToken);
    } catch (error) {
      return next(new ErrorResponse("Invalid or expired Google token", 401));
    }

    // Check if user exists in our database
    const user = await User.findOne({ email: decodedToken.email });

    if (!user) {
      return next(new ErrorResponse("User does not exist. Please sign up first.", 404));
    }

    // Update user's last login and verification status
    user.lastLogin = Date.now();
    user.isVerified = true;
    await user.save({ validateBeforeSave: false });

    // Send token response
    sendTokenResponse(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Google Sign-Up (for new users with role selection)
// @route   POST /api/auth/google-signup
// @access  Public
exports.googleSignup = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { idToken, role } = req.body;

    // Verify the Firebase ID token
    let decodedToken;
    try {
      decodedToken = await verifyIdToken(idToken);
    } catch (error) {
      return next(new ErrorResponse("Invalid or expired Google token", 401));
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: decodedToken.email });
    if (existingUser) {
      return next(new ErrorResponse("User already exists. Please sign in instead.", 400));
    }

    // Extract user information from Google token
    const { email, name, picture, email_verified } = decodedToken;

    // Split name into first and last name
    const nameParts = name ? name.split(' ') : ['', ''];
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    // Create new user
    const user = await User.create({
      firstName,
      lastName,
      email,
      mobile: '', // Google users don't have mobile initially
      role: role || 'buyer',
      profileImage: picture || 'default-profile.jpg',
      isVerified: email_verified || true,
      lastLogin: Date.now()
    });

    // Send token response
    sendTokenResponse(user, 201, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Toggle user role between buyer and seller
// @route   POST /api/auth/toggle-role
// @access  Private
exports.toggleRole = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    // Check if user can toggle roles (non-admin only)
    if (!user.canToggleRoles()) {
      return next(new ErrorResponse('Admin users cannot toggle roles', 403));
    }

    // Toggle the active role
    const newActiveRole = user.toggleActiveRole();
    await user.save({ validateBeforeSave: false });

    // Prepare updated user data to send
    const userData = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      mobile: user.mobile,
      profileImage: user.profileImage,
      role: user.role,
      activeRole: user.activeRole,
      isVerified: user.isVerified,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      // Include sellerInfo when user is in seller mode (either role=seller or activeRole=seller)
      ...(user.activeRole === 'seller' && { sellerInfo: user.sellerInfo }),
      // Include paymentInfo for all users (needed for Stripe configuration)
      ...(user.paymentInfo && { paymentInfo: user.paymentInfo }),
    };

    res.status(200).json({
      success: true,
      message: `Role switched to ${newActiveRole}`,
      data: userData,
    });
  } catch (err) {
    next(err);
  }
};
