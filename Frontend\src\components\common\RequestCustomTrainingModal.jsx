import React, { useState } from "react";
import { FaTimes, FaCheck } from "react-icons/fa";
import "../../styles/RequestCustomTrainingModal.css";
import Thankyou from "../../assets/images/thankyou.svg";
const RequestCustomTrainingModal = ({ isOpen, onClose /*, strategy*/ }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: ""
  });
  const [showSuccessState, setShowSuccessState] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log("Custom training request submitted:", formData);
    // Show success state
    setShowSuccessState(true);
  };

  const handleCloseModal = () => {
    // Reset form and success state
    setFormData({
      name: "",
      email: "",
      phone: "",
      message: ""
    });
    setShowSuccessState(false);
    onClose();
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="request-modal-overlay" onClick={handleOverlayClick}>
      <div className={`request-modal ${showSuccessState ? 'request-modal--success' : ''}`}>
        <div className="request-modal__header">
          <h2 className="request-modal__title">
            {showSuccessState ? "Your request is submitted successfully!" : "Request Custom Training"}
          </h2>
          <button className="request-modal__close" onClick={handleCloseModal}>
            <FaTimes />
          </button>
        </div>

        <div className="request-modal__content">
          {showSuccessState ? (
            <div className="request-modal__success">
              <div className="success-icon">
                   <img src={Thankyou} alt="thankyou" />
              </div>
              <p className="success-message">
                Your custom training request has been submitted successfully!
                We will get back to you soon.
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="request-modal__form">
               <div className="request-modal__form-group">
             
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="request-modal__textarea"
                  placeholder="What are you requesting?"
                  rows="4"
                  required
                />
              </div>
              <div className="request-modal__form-group">
               
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="request-modal__input"
                  placeholder="Enter your requesting amount"
                  required
                />
              </div>

           

         

             

              <button type="submit" className="btn-primary">
                Send Request
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestCustomTrainingModal;
