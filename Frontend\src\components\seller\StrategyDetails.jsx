import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import SellerLayout from "./SellerLayout";
import DocumentViewer from "../common/DocumentViewer";
import {
  getSellerContentById,
  deleteContent,
} from "../../redux/slices/contentSlice";
import { toast } from "react-toastify";
import "../../styles/StrategyDetails.css";
import ourmissionimage from "../../assets/images/ourmissionimage.svg";
import {
  getImageUrl,
  getSmartFileUrl,
  getLocalFallbackUrl,
} from "../../utils/constants";
// Icons
import { MdPlayArrow, MdOutlineModeEdit, MdDownload } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import ConfirmationModal from "../common/ConfirmationModal";
import PreviewContent from "../common/PreviewContent";
import { FaEdit, FaEye, FaTrash, FaDownload, FaSpinner } from "react-icons/fa";
import { formatStandardDate } from "../../utils/dateValidation";

const StrategyDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { singleContent, isLoading, error } = useSelector(
    (state) => state.content
  );

  // State for delete confirmation modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // State for video player
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [showFullVideo, setShowFullVideo] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState(null);

  // Fetch content data when component mounts or ID changes
  useEffect(() => {
    if (id && (!singleContent || singleContent._id !== id)) {
      dispatch(getSellerContentById(id));
    }
  }, [dispatch, id, singleContent]);

  // Refresh data when returning from edit page - only on navigation state change
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only refresh if coming from edit page (check session storage or URL history)
      const previousPath = sessionStorage.getItem("previousPath");
      if (
        id &&
        document.visibilityState === "visible" &&
        previousPath &&
        previousPath.includes("/edit")
      ) {
        dispatch(getSellerContentById(id));
        sessionStorage.removeItem("previousPath"); // Clear after use
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [dispatch, id]);

  // Handle error display
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to load strategy details");
    }
  }, [error]);

  // Handle edit button click
  const handleEdit = () => {
    // Store current path to know we're coming from edit page
    sessionStorage.setItem(
      "previousPath",
      `/seller/strategy-details/${id}/edit`
    );
    navigate(`/seller/strategy-details/${id}/edit`);
  };

  // Handle delete button click
  const handleDelete = () => {
    setShowDeleteModal(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      await dispatch(deleteContent(id)).unwrap();
      toast.success("Strategy deleted successfully");
      navigate("/seller/my-sports-strategies");
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error(error.message || "Failed to delete strategy");
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  };

  // Handle play/pause toggle
  const handlePlayPause = () => {
    if (singleContent?.fileUrl) {
      const fileExtension = singleContent.fileUrl
        .split(".")
        .pop()
        .toLowerCase();
      const videoExtensions = [
        "mp4",
        "avi",
        "mov",
        "wmv",
        "flv",
        "webm",
        "mkv",
      ];



      if (videoExtensions.includes(fileExtension)) {
        setShowVideo(!showVideo);
        if (!showVideo) {
          setIsPlaying(true);
        }
      } else {
        // For non-video files, download them
        handleDownload();
      }
    } else {
      toast.error("No file available");
    }
  };

  // Handle file download - DISABLED FOR SECURITY
  const handleDownload = () => {
    toast.error("Download functionality has been disabled for security purposes");
  };

  // Check if file is a video
  const isVideoFile = () => {
    if (!singleContent?.fileUrl) return false;
    const fileExtension = singleContent.fileUrl.split(".").pop().toLowerCase();
    const videoExtensions = ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"];
    return videoExtensions.includes(fileExtension);
  };

  // Helper functions for formatting data
  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (item) => {
    let price;

    // For auction items, use basePrice from auctionDetails
    if (item?.saleType === "Auction" && item?.auctionDetails?.basePrice) {
      price = item.auctionDetails.basePrice;
    } else {
      // For fixed price items or fallback
      price = item?.price;
    }

    return typeof price === "number" ? `$${price.toFixed(2)}` : "$0.00";
  };

  const formatDuration = (duration) => {
    if (!duration) return "N/A";
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    return hours > 0
      ? `${hours}:${minutes.toString().padStart(2, "0")}:00`
      : `${minutes}:00`;
  };

  // Check if auction has started (making it uneditable)
  const isAuctionStarted = () => {
    if (!singleContent || singleContent.saleType !== "Auction") return false;

    const now = new Date();
    const startDate = singleContent.auctionDetails?.auctionStartDate
      ? new Date(singleContent.auctionDetails.auctionStartDate)
      : null;

    return startDate && now >= startDate;
  };

  // Show loading state
  if (isLoading) {
    return (
      <SellerLayout>
        <div className="StrategyDetails">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategy details...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Show error state if no content found
  if (!singleContent) {
    return (
      <SellerLayout>
        <div className="StrategyDetails">
          <div className="error-container">
            <h3>Strategy not found</h3>
            <p>
              The strategy you're looking for doesn't exist or has been removed.
            </p>
            <button
              className="btn btn-primary"
              onClick={() => navigate("/seller/my-sports-strategies")}
            >
              Back to Strategies
            </button>
          </div>
        </div>
      </SellerLayout>
    );
  }

  const strategy = singleContent;

  return (
    <SellerLayout>
      <div className="StrategyDetails">
        {/* Video/Document Info Section */}
        <div className="StrategyDetails__info-header">
          <div className="StrategyDetails__info-content">
            <h2 className="StrategyDetails__info-title">Video/Document Info</h2>
          </div>
          <div className="StrategyDetails__info-actions">
            <button
              className={`StrategyDetails__edit-btn ${isAuctionStarted() ? 'disabled' : ''}`}
              onClick={isAuctionStarted() ? undefined : handleEdit}
              disabled={isAuctionStarted()}
              title={isAuctionStarted() ? "You can't edit the strategy once the auction has started." : "Edit Strategy"}
              style={{
                cursor: isAuctionStarted() ? 'not-allowed' : 'pointer',
                opacity: isAuctionStarted() ? 0.6 : 1
              }}
            >
              <MdOutlineModeEdit className="StrategyDetails__action-icon" />
              Edit
            </button>
            <button
              className="StrategyDetails__delete-btn"
              onClick={handleDelete}
            >
              <RiDeleteBin6Line className="StrategyDetails__action-icon" />
              Delete
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="StrategyDetails__content">
          <h3 className="StrategyDetails__strategy-title">{strategy.title}</h3>
          {/* Strategy Media */}
          <div className="StrategyDetails__media-container">
            {showVideo && isVideoFile() ? (
              <div className="StrategyDetails__video-wrapper">
                <video
                  className="StrategyDetails__video"
                  controls
                  autoPlay={isPlaying}
                  controlsList="nodownload nofullscreen noremoteplayback"
                  disablePictureInPicture
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onError={(e) => {
                    console.error("Video error:", e);

                    // Try fallback URL if current video fails
                    const currentSrc = e.target.src;
                    const primaryUrl = getSmartFileUrl(
                      showFullVideo
                        ? strategy.fileUrl
                        : strategy.previewUrl || strategy.fileUrl
                    );
                    const fallbackUrl = getLocalFallbackUrl(
                      showFullVideo
                        ? strategy.fileUrl
                        : strategy.previewUrl || strategy.fileUrl
                    );

                    if (
                      currentSrc === primaryUrl &&
                      fallbackUrl &&
                      fallbackUrl !== primaryUrl
                    ) {
                      console.log("Trying fallback URL:", fallbackUrl);
                      setCurrentVideoUrl(fallbackUrl);
                      e.target.src = fallbackUrl;
                      e.target.load();
                    } else {
                      toast.error("Failed to load video. Please try again.");
                    }
                  }}
                  onLoadStart={() => console.log("Video loading started")}
                  onCanPlay={() => console.log("Video can play")}
                  poster={
                    getSmartFileUrl(
                      strategy.thumbnailUrl || strategy.previewUrl
                    ) || ourmissionimage
                  }
                >
                  {/* Use preview URL for preview mode, full file URL for full video mode */}
                  <source
                    src={
                      currentVideoUrl ||
                      getSmartFileUrl(
                        showFullVideo
                          ? strategy.fileUrl
                          : strategy.previewUrl || strategy.fileUrl
                      )
                    }
                    type="video/mp4"
                  />
                  Your browser does not support the video tag.
                </video>
                {/* <div className="StrategyDetails__video-controls">
                  {strategy.previewUrl && strategy.previewUrl !== strategy.fileUrl && (
                    <button
                      className="StrategyDetails__control-btn"
                      onClick={() => setShowFullVideo(!showFullVideo)}
                      title={showFullVideo ? "Show Preview" : "Show Full Video"}
                    >
                      {showFullVideo ? "Preview" : "Full Video"}
                    </button>
                  )}
                  <button
                    className="StrategyDetails__control-btn"
                    onClick={() => setShowVideo(false)}
                    title="Hide Video"
                  >
                    Close Video
                  </button>
                  <button
                    className="StrategyDetails__control-btn"
                    onClick={handleDownload}
                    title="Download"
                  >
                    <MdDownload /> Download
                  </button>
                </div> */}
              </div>
            ) : strategy.contentType === "PDF" ||
              strategy.contentType === "Document" ? (
              // Document Preview Section
              <div className="StrategyDetails__document-container">
                <div className="StrategyDetails__document-viewer">
                  <DocumentViewer
                    fileUrl={getSmartFileUrl(strategy.fileUrl)}
                    fileName={strategy.fileUrl?.split("/").pop() || "document"}
                    title={strategy.title}
                    className="StrategyDetails__document-element"
                    height="500px"
                    showDownload={false}
                  />
                </div>
              </div>
            ) : (
              <div className="StrategyDetails__image-container">
                <img
                  src={
                    getSmartFileUrl(
                      strategy.thumbnailUrl || strategy.previewUrl
                    ) || ourmissionimage
                  }
                  alt={strategy.title}
                  className="StrategyDetails__image"
                />
                <div
                  className="StrategyDetails__play-overlay"
                  onClick={handlePlayPause}
                  title={
                    isVideoFile()
                      ? strategy.previewUrl &&
                        strategy.previewUrl !== strategy.fileUrl
                        ? "Play Preview (10 seconds)"
                        : "Play Video"
                      : strategy.fileUrl
                        ? "Download File"
                        : "No file available"
                  }
                >
                  {strategy.fileUrl ? (
                    <>
                      {isVideoFile() ? (
                        <>
                          <MdPlayArrow className="StrategyDetails__play-icon" />
                          <span className="StrategyDetails__overlay-text">
                            {strategy.previewUrl &&
                              strategy.previewUrl !== strategy.fileUrl
                              ? "Play Preview"
                              : "Play Video"}
                          </span>
                        </>
                      ) : (
                        <>
                          <MdDownload className="StrategyDetails__play-icon" />
                          <span className="StrategyDetails__overlay-text">
                            Download
                          </span>
                        </>
                      )}
                    </>
                  ) : (
                    <MdPlayArrow className="StrategyDetails__play-icon" />
                  )}
                </div>
              </div>
            )}
          </div>
          {/* Description Section */}
          <div className="StrategyDetails__description-section">
            <h3 className="StrategyDetails__section-title">Description</h3>
            <PreviewContent
              html={strategy.description}
              className="StrategyDetails__description"
              ariaLabel="Strategy description preview"
            />
          </div>
          {/* Coach Info */}
          <div className="StrategyDetails__coach-section">
            <h3 className="StrategyDetails__section-title">The Coach</h3>
            <div className="StrategyDetails__coach-info">
              <div className="StrategyDetails__coach-details">
                <h4 className="StrategyDetails__coach-name">
                  {strategy.coachName || "Coach Name"}
                </h4>
                <p className="StrategyDetails__coach-title">
                  {strategy.sport} Specialist
                </p>
                <PreviewContent
                  html={strategy.aboutCoach}
                  className="StrategyDetails__coach-description"
                  ariaLabel="Coach description preview"
                />
              </div>
            </div>
          </div>

          <div className="StrategyDetails__stats-and-steps">
            {/* Key Stats Section */}
            <div className="StrategyDetails__stats-section">
              <h3 className="StrategyDetails__section-title">
                Strategic Content Info
              </h3>
              <div className="StrategyDetails__stats-grid">
                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Category</span>
                  <span className="StrategyDetails__stat-value">
                    {strategy.category}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Sport</span>
                  <span className="StrategyDetails__stat-value">
                    {strategy.sport}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Duration</span>
                  <span className="StrategyDetails__stat-value">
                    {formatDuration(strategy.duration)}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Price</span>
                  <span className="StrategyDetails__stat-value">
                    {formatPrice(strategy)}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">
                    Content Type
                  </span>
                  <span className="StrategyDetails__stat-value">
                    {strategy.contentType}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">
                    Difficulty
                  </span>
                  <span className="StrategyDetails__stat-value">
                    {strategy.difficulty}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Status</span>
                  <span className="StrategyDetails__stat-value">
                    {strategy.status}
                  </span>
                </div>

                <div className="StrategyDetails__stat-content">
                  <span className="StrategyDetails__stat-label">Created</span>
                  <span className="StrategyDetails__stat-value">
                    {formatDate(strategy.createdAt)}
                  </span>
                </div>
              </div>
            </div>
            <div className="vertical-line"></div>
            {/* Strategy Steps */}
            <div className="StrategyDetails__steps-section">
              <h3 className="StrategyDetails__section-title">
                This strategic content Includes
              </h3>
              <div className="StrategyDetails__steps-list">
                <div className="StrategyDetails__step">
                  <PreviewContent
                    html={strategy.strategicContent}
                    className="StrategyDetails__step-text"
                    ariaLabel="Strategic content preview"
                  />
                </div>

                {strategy.tags && strategy.tags.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">Tags:</span>
                    <span className="StrategyDetails__step-text">
                      {strategy.tags.join(", ")}
                    </span>
                  </div>
                )}

                {strategy.prerequisites &&
                  strategy.prerequisites.length > 0 && (
                    <div className="StrategyDetails__step">
                      <span className="StrategyDetails__step-label">
                        Prerequisites:
                      </span>
                      <span className="StrategyDetails__step-text">
                        {strategy.prerequisites.join(", ")}
                      </span>
                    </div>
                  )}

                {strategy.learningObjectives &&
                  strategy.learningObjectives.length > 0 && (
                    <div className="StrategyDetails__step">
                      <span className="StrategyDetails__step-label">
                        Learning Objectives:
                      </span>
                      <span className="StrategyDetails__step-text">
                        {strategy.learningObjectives.join(", ")}
                      </span>
                    </div>
                  )}

                {strategy.equipment && strategy.equipment.length > 0 && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">
                      Equipment Needed:
                    </span>
                    <span className="StrategyDetails__step-text">
                      {strategy.equipment.join(", ")}
                    </span>
                  </div>
                )}

                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-label">Language:</span>
                  <span className="StrategyDetails__step-text">
                    {strategy.language}
                  </span>
                </div>

                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-label">
                    Sale Type:
                  </span>
                  <span className="StrategyDetails__step-text">
                    {strategy.saleType}
                  </span>
                </div>

                {strategy.fileSize && (
                  <div className="StrategyDetails__step">
                    <span className="StrategyDetails__step-label">
                      File Size:
                    </span>
                    <span className="StrategyDetails__step-text">
                      {(strategy.fileSize / 1024 / 1024).toFixed(2)} MB
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDeleteConfirm}
          title="Delete Strategy"
          message={`Are you sure you want to delete "${strategy?.title}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
          isLoading={isDeleting}
        />
      </div>
    </SellerLayout>
  );
};

export default StrategyDetails;
