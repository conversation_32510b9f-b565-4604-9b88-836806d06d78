.buyer-dashboard {
  padding: 30px 0;
  min-height: calc(100vh - 90px);
  position: relative;
}

.buyer-dashboard .max-container {
  margin: 0 auto;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 30px;
  padding: 0 20px;
}

/* Filter Toggle Button */
.buyer-dashboard .filter-toggle-btn {
  display: none;
  background-color: var(--second-primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
}

.buyer-dashboard .filter-toggle-btn:hover {
  transform: scale(1.02);
}

/* Filter Overlay */
.buyer-dashboard .filter-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Filter Section Styles */
.buyer-dashboard .filter-section {
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  padding: 25px;
  position: sticky;
  top: 100px;
  height: fit-content;
  border: 1px solid rgba(0, 0, 0, 0.05);
  /* min-height: 100vh; */
  overflow-y: auto;
  transition: transform 0.3s ease;
}

/* Close Drawer Button */
.buyer-dashboard .close-drawer-btn {
  display: none;
  position: absolute;
  top: -5px;
  right: -5px;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 5px;
  transition: all 0.2s ease;
}

.buyer-dashboard .close-drawer-btn:hover {
  color: var(--btn-color);
  transform: scale(1.1);
}

.buyer-dashboard .filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.buyer-dashboard .filter-header h3 {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: var(--font-semibold);
  margin: 0;
  position: relative;
}

.buyer-dashboard .filter-header h3::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--btn-color);
  border-radius: var(--border-radius);
}

.buyer-dashboard .clear-all {
  color: var(--btn-color);
  background: none;
  border: none;
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: var(--font-semibold);
}

.buyer-dashboard .clear-all:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.buyer-dashboard .filter-group {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.buyer-dashboard .filter-group:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.buyer-dashboard .filter-group h4 {
  font-size: var(--basefont);
  color: var(--secondary-color);
  margin-bottom: 15px;
  font-weight: var(--font-semibold);
  display: flex;
  align-items: center;
}

.buyer-dashboard .sport-select-wrapper {
  position: relative;
}

.buyer-dashboard .sport-select-wrapper::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid var(--secondary-color);
  pointer-events: none;
}

.buyer-dashboard .sport-select {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  appearance: none;
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.buyer-dashboard .sport-select:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.buyer-dashboard .checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-left: 5px;
}

.buyer-dashboard .checkbox-item {
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.2s ease;
}

.buyer-dashboard .checkbox-item:hover {
  transform: translateX(2px);
}

.buyer-dashboard .checkbox-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--btn-color);
  cursor: pointer;
  border-radius: var(--border-radius);
}

.buyer-dashboard .checkbox-item label {
  font-size: var(--smallfont);
  color: var(--text-color);
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
}

.buyer-dashboard .checkbox-item:hover label {
  color: var(--btn-color);
}

.buyer-dashboard .price-range {
  margin-top: 15px;
}

.buyer-dashboard .price-slider-container {
  position: relative;
  width: 100%;
  height: 20px;
  margin-bottom: 15px;
}

.buyer-dashboard .price-slider-container::before {
  content: "";
  position: absolute;
  top: 20%;
  left: 0;
  right: 0;
  height: 4px;
  background-color: var(--light-gray);
  transform: translateY(-50%);
  border-radius: var(--border-radius);
}

.buyer-dashboard .price-slider-container::after {
  content: "";
  position: absolute;
  top: 20%;
  height: 4px;
  background-color: var(--btn-color);
  transform: translateY(-50%);
  border-radius: var(--border-radius);
  z-index: 0;
  width: calc(
    100% * (var(--max-value, 5000) - var(--min-value, 0)) /
      var(--max-price, 5000)
  );
  left: calc(100% * var(--min-value, 0) / var(--max-price, 5000));
  transition: background-color 0.3s ease;
}

.buyer-dashboard .price-slider-container.active::after {
  background-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(255, 107, 53, 0.3);
}

.buyer-dashboard .price-slider {
  position: absolute;
  width: 100%;
  height: 5px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  z-index: 3;
}

.buyer-dashboard .price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--white);
  cursor: pointer;
  pointer-events: auto;
  border: 2px solid var(--btn-color);
  box-shadow: var(--box-shadow-light);
  transition: all 0.2s ease;
}

.buyer-dashboard .price-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 12px rgba(238, 52, 37, 0.3);
}

.buyer-dashboard
  .price-slider-container.active
  .price-slider::-webkit-slider-thumb {
  border-color: var(--primary-color);
  box-shadow: 0 0 12px rgba(255, 107, 53, 0.4);
}

.buyer-dashboard .price-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--white);
  cursor: pointer;
  pointer-events: auto;
  border: 2px solid var(--btn-color);
  box-shadow: var(--box-shadow-light);
  transition: all 0.2s ease;
}

.buyer-dashboard .price-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 12px rgba(238, 52, 37, 0.3);
}

.buyer-dashboard
  .price-slider-container.active
  .price-slider::-moz-range-thumb {
  border-color: var(--primary-color);
  box-shadow: 0 0 12px rgba(255, 107, 53, 0.4);
}

.buyer-dashboard .min-slider {
  z-index: 2;
}

.buyer-dashboard .max-slider {
  z-index: 1;
}

.buyer-dashboard .price-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: var(--font-semibold);
}

/* Content Section Styles */
.buyer-dashboard .content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.buyer-dashboard .content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.buyer-dashboard .content-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.buyer-dashboard .retry-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--btn-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.buyer-dashboard .retry-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.1);
}

.buyer-dashboard .content-title {
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: var(--font-semibold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.buyer-dashboard .content-title span {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: normal;
}

.buyer-dashboard .search-sort {
  display: grid;
  align-items: center;
  gap: 15px;
  grid-template-columns: 1fr 1fr;
}

.buyer-dashboard .search-container {
  position: relative;
  width: 210px;
}

.buyer-dashboard .search-input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

.buyer-dashboard .search-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--dark-gray);
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.buyer-dashboard .sort-container {
  display: grid;
  align-items: center;
  gap: 10px;
}

.buyer-dashboard .sort-container label {
  font-size: var(--heading4);
  color: var(--text-color);
  display: flex;
}

.buyer-dashboard .sort-select {
  padding: 10px 10px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--text-color);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24'%3E%3Cpath fill='%23163351' d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 12px;
}

/* Strategy Grid Styles */
.buyer-dashboard .strategy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

/* Empty State Styles */
.buyer-dashboard .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: var(--primary-light-color);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
  grid-column: 1 / -1;
}

.buyer-dashboard .empty-state h3 {
  font-size: var(--heading5);
  color: var(--secondary-color);
  margin-bottom: 8px;
}

.buyer-dashboard .empty-state p {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin-bottom: 20px;
}

.buyer-dashboard .clear-filters-btn {
  background-color: var(--btn-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
}

.buyer-dashboard .clear-filters-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
}

/* Error Styles */
.buyer-dashboard .strategies-error {
  grid-column: 1 / -1;
  margin: 20px 0;
}

/* Pagination Styles */
.buyer-dashboard .pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

.buyer-dashboard .pagination-item,
.buyer-dashboard .pagination-arrow {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.buyer-dashboard .pagination-item.active {
  background-color: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
}

.buyer-dashboard .pagination-item:hover,
.buyer-dashboard .pagination-arrow:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  color: var(--white);
}

.buyer-dashboard .pagination-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.buyer-dashboard .pagination-ellipsis {
  width: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--smallfont);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .buyer-dashboard .max-container {
    grid-template-columns: 230px 1fr;
  }

  .buyer-dashboard .strategy-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .buyer-dashboard .content-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .buyer-dashboard .search-sort {
    width: 100%;
    align-items: flex-start;
  }

  .buyer-dashboard .search-container {
    width: 100%;
  }

  .buyer-dashboard .sort-container {
    width: 100%;
  }

  .buyer-dashboard .sort-select {
    width: 100%;
  }

  .buyer-dashboard .strategy-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .buyer-dashboard .strategy-grid {
    grid-template-columns: 1fr;
  }
}

/* Media Queries */
@media (max-width: 680px) {
  .buyer-dashboard .max-container {
    grid-template-columns: 1fr;
  }

  .buyer-dashboard .filter-toggle-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: fit-content;
  }

  .buyer-dashboard .content-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .buyer-dashboard .search-sort {
    width: auto;
  }

  .buyer-dashboard .filter-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    z-index: 999;
    transform: translateX(-100%);
    border-radius: 0;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
  }

  .buyer-dashboard .filter-section.drawer-open {
    transform: translateX(0);
  }

  .buyer-dashboard .filter-overlay {
    display: block;
    opacity: 1;
  }

  .buyer-dashboard .close-drawer-btn {
    display: block;
  }
}
@media (max-width: 330px) {
  .buyer-dashboard .content-title {
    flex-direction: column;
  }
}
